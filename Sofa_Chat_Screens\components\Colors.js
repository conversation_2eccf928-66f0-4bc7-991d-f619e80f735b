// theme.js
export default {
  // Extracted from screenshot
    primary: '#00987E',
    primary50: '#f0f5f3',  
    primary100: '#d6e4e0', 
    primary200: '#a8c5bd',
    primary400: '#6b9080', 
    primary500: '#4a7a6b',
    primary700: '#2d5c50',  
    primary800: '#1a3a32',  
    primary900: '#0d1117',  
    text: '#000000',         
    subtext: '#666666',        
    inputBorder: '#CCCCCC',   
    inputPlaceholder: '#999999',

  // Complementary colors for theme
  secondary: '#5AC8FA',      // Light blue for secondary elements
  success: '#4CD964',        // Green for success states
  warning: '#FFCC00',        // Yellow for warnings
  danger: '#FF3B30',        // Red for errors/destructive actions
  accent: '#5856D6',         // Purple for accents
  lightText: '#FFFFFF',      // Text on dark backgrounds
  darkBackground: '#1C1C1E', // Dark background for dark mode
  cardDark: '#2C2C2E',       // Dark card background
  highlight: '#FF9500',      // Orange for highlights
  muted: '#8E8E93',          // Muted text/icons
  
  // Additional UI colors
  buttonDisabled: '#A7A7A7', // Disabled button color
  separator: '#E5E5EA',      // Separator lines
  overlay: 'rgba(0,0,0,0.5)', // Overlay color
  gray500: 'rgb(150, 165, 175)',  // Medium gray (text)
  gray700: 'rgb(117, 132, 142)',  // Dark gray (headings)
  gray800: 'rgb(50, 50, 50)',  // Dark gray (headings)
  gray700: 'rgb(117, 132, 142)',  // Dark gray (headings)
};
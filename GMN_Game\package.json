{"name": "gmn_game", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.9", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.4", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4", "expo-dev-client": "~5.1.8"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}
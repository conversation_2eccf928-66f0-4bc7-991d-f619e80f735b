import { Pressable, StyleSheet, Text, View } from "react-native";

function GoalItems(props)
{
    return (
        <View style={styles.goalItem}>
        <Pressable 
        android_ripple={{color: 'white'}}
        style={({pressed}) => pressed && styles.pressedItem}
        onPress={props.onDeleteGoal.bind(this, props.id)}
        >
                <Text style={styles.goalText}>{props.text}</Text>
              </Pressable>
            </View>

            );
}

export default GoalItems;

const styles = StyleSheet.create({
  goalItem: {
    marginTop: 8,
    marginRight: 8,
    marginLeft: 1,
    marginBottom: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "white",
    backgroundColor: "green",
  },
  goalText: {
    color: "white",
    fontSize: 16,
    padding: 8,
    
  },
  pressedItem: {
    opacity: 0.5,
  }, // for android ripple effect
});
import { StatusBar } from "expo-status-bar";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Ionicons } from "@expo/vector-icons";

// Expense-related screens and components
import ManageExpense from "./screens/ManageExpense";
import RecentExpences from "./screens/RecentExpences";
import AllExpences from "./screens/AllExpenses";
import { GlobalStyles } from "./constants/styles";
import IconButton from "./components/UI/IconButton";
import ExpensesContextProvider from "./store/expense-context";

// Auth-related screens
import LoginScreen from './screens/LoginScreen';
import SignupScreen from './screens/SignupScreen';
import WelcomeScreen from './screens/WelcomeScreen';

const Stack = createNativeStackNavigator();
const BottomTabs = createBottomTabNavigator();
const AuthStack = createNativeStackNavigator();

function ExpensesOverview() {
  return (
    <BottomTabs.Navigator
      screenOptions={({ navigation }) => ({
        headerStyle: { backgroundColor: GlobalStyles.colors.primary500 },
        headerTintColor: "white",
        tabBarStyle: { backgroundColor: GlobalStyles.colors.primary500 },
        tabBarActiveTintColor: GlobalStyles.colors.accent500,
        tabBarInactiveTintColor: "white",
        headerRight: ({ tintColor }) => (
          <IconButton
            icon="add"
            color={tintColor}
            size={24}
            onPress={() => {
              navigation.navigate("ManageExpense");
            }}
          />
        ),
      })}
    >
      <BottomTabs.Screen
        name="RecentExpenses"
        component={RecentExpences}
        options={{
          title: "Recent Expenses",
          tabBarLabel: "Recent",
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="hourglass" size={size} color={color} />
          ),
        }}
      />
      <BottomTabs.Screen
        name="AllExpenses"
        component={AllExpences}
        options={{
          title: "All Expenses",
          tabBarLabel: "All Expenses",
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="calendar" size={size} color={color} />
          ),
        }}
      />
    </BottomTabs.Navigator>
  );
}

function AuthStackScreen() {
  return (
    <AuthStack.Navigator
      screenOptions={{
        headerStyle: { backgroundColor: GlobalStyles.colors.primary500 },
        headerTintColor: 'white',
        contentStyle: { backgroundColor: GlobalStyles.colors.primary100 },
      }}
    >
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Signup" component={SignupScreen} />
    </AuthStack.Navigator>
  );
}

function AuthenticatedStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: { backgroundColor: GlobalStyles.colors.primary500 },
        headerTintColor: "white",
      }}
    >
      <Stack.Screen
        name="ExpensesOverview"
        component={ExpensesOverview}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ManageExpense"
        component={ManageExpense}
        options={{ title: "Manage Expense", presentation: "modal" }}
      />
    </Stack.Navigator>
  );
}

function Root() {
  // Here you would typically check auth state
  // For now, I'll assume isAuthenticated is false initially
  const isAuthenticated = false;

  return (
    <NavigationContainer>
      {isAuthenticated ? <AuthenticatedStack /> : <AuthStackScreen />}
    </NavigationContainer>
  );
}

export default function App() {
  return (
    <>
      <StatusBar style="light" />
      <ExpensesContextProvider>
        <Root />
      </ExpensesContextProvider>
    </>
  );
}
// import { StatusBar } from "expo-status-bar";
// import { NavigationContainer } from "@react-navigation/native";
// import { createNativeStackNavigator } from "@react-navigation/native-stack";
// import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
// import { Ionicons } from "@expo/vector-icons";

// import ManageExpense from "./screens/ManageExpense";
// import RecentExpences from "./screens/RecentExpences";
// import AllExpences from "./screens/AllExpenses";
// import { GlobalStyles } from "./constants/styles";
// import IconButton from "./components/UI/IconButton";
// import ExpensesContextProvider from "./store/expense-context";

// const Stack = createNativeStackNavigator();
// const BottomTabs = createBottomTabNavigator();

// function ExpensesOverview() {
//   return (
//     <BottomTabs.Navigator
//       screenOptions={({ navigation }) => ({
//         headerStyle: { backgroundColor: GlobalStyles.colors.primary500 },
//         headerTintColor: "white",
//         tabBarStyle: { backgroundColor: GlobalStyles.colors.primary500 },
//         tabBarActiveTintColor: GlobalStyles.colors.accent500,
//         tabBarInactiveTintColor: "white",
//         headerRight: ({ tintColor }) => (
//           <IconButton
//             icon="add"
//             color={tintColor}
//             size={24}
//             onPress={() => {
//               navigation.navigate("ManageExpense");
//             }}
//           />
//         ),
//       })}
//       >
//       <BottomTabs.Screen
//         name="RecentExpenses"
//         component={RecentExpences}
//         options={{
//           title: "Recent Expenses",
//           tabBarLabel: "Recent",
//           tabBarIcon: ({ color, size }) => (
//             <Ionicons name="hourglass" size={size} color={color} />
//           ),
//         }}
//       />
//       <BottomTabs.Screen
//         name="AllExpenses"
//         component={AllExpences}
//         options={{
//           title: "All Expenses",
//           tabBarLabel: "All Expenses",
//           tabBarIcon: ({ color, size }) => (
//             <Ionicons name="calendar" size={size} color={color} />
//           ),
//         }}
//       />
//     </BottomTabs.Navigator>
//   );
// }

// export default function App() {
//   return (
//     <>
//       <StatusBar style="light" />
//       <ExpensesContextProvider>
//         <NavigationContainer>
//           <Stack.Navigator
//             screenOptions={{
//               headerStyle: { backgroundColor: GlobalStyles.colors.primary500 },
//               headerTintColor: "white",
//             }}
//           >
//             <Stack.Screen
//               name="ExpensesOverview"
//               component={ExpensesOverview}
//               options={{ headerShown: false }}
//             />
//             <Stack.Screen
//               name="ManageExpense"
//               component={ManageExpense}
//               options={{ title: "Manage Expense", presentation: "modal" }}
//             />
//           </Stack.Navigator>
//         </NavigationContainer>
//       </ExpensesContextProvider>
//     </>
//   );
// }

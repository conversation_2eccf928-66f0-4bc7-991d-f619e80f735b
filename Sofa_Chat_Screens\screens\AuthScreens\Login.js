import {
  View,
  Text,
  TextInput,
  Pressable,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Dimensions,
  I18nManager,
  Image,
} from "react-native";
import { useState, useRef } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { MaterialIcons } from "@expo/vector-icons";
import { CommonHeader } from "../../components/CommonHeader";
import Colors from "../../components/Colors";
import { useNavigation } from "@react-navigation/native";

const { height, width } = Dimensions.get("window");

// Force RTL layout for Arabic
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const LogIn = () => {
  const navigation = useNavigation();
  const [serverUrl, setServerUrl] = useState("");
  const [serverName, setServerName] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const scrollViewRef = useRef(null);

  const handleConnect = () => {
    console.log("Loged In");
    navigation.replace("MainTabs");
  };

  const scrollToInput = (inputPosition) => {
    if (scrollViewRef.current) {
      // Calculate maximum scroll position to keep subtitle visible
      const maxScrollY = Math.max(0, inputPosition - height * 0.3);
      const minScrollY = 0;
      const targetScrollY = Math.min(
        maxScrollY,
        Math.max(minScrollY, inputPosition - height * 0.4)
      );

      scrollViewRef.current.scrollTo({
        y: targetScrollY,
        animated: true,
      });
    }
  };

  return (
    <LinearGradient
      colors={["#E5F9F6", "#FFFFFF", "#E5F9F6", "#E5F9F6"]}
      style={styles.LinearGradientContainer}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.5, y: 1 }}
    >
      <CommonHeader />
      <SafeAreaView style={styles.container}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          scrollEventThrottle={16}
          onScroll={(event) => {
            const scrollY = event.nativeEvent.contentOffset.y;
            const maxScrollY = height * 0.25; // Limit scroll to keep subtitle visible
            if (scrollY > maxScrollY) {
              scrollViewRef.current?.scrollTo({
                y: maxScrollY,
                animated: false,
              });
            }
          }}
        >
          <View style={styles.content}>
            {/* Header */}
            <Text style={styles.subtitle}>تسجيل الدخول</Text>
            <Text style={styles.instruction}>
              ادخل البيانات الخاصة بك للدخول.
            </Text>

            {/* Form */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                البريد الإلكتروني أو اسم المستخدم
              </Text>
              <TextInput
                key="email-input-stable"
                style={styles.input}
                placeholder="البريد الإلكتروني أو اسم المستخدم"
                placeholderTextColor="#999"
                value={serverUrl}
                onChangeText={setServerUrl}
                textAlign="right"
                textAlignVertical="center"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="next"
                enablesReturnKeyAutomatically={false}
                onFocus={() => scrollToInput(100)}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>كلمة المرور</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  key="password-input-stable"
                  style={styles.passwordInput}
                  placeholder="كلمة المرور"
                  placeholderTextColor="#999"
                  value={serverName}
                  onChangeText={setServerName}
                  textAlign="right"
                  textAlignVertical="center"
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  keyboardType="default"
                  textContentType="password"
                  returnKeyType="done"
                  onFocus={() => scrollToInput(200)}
                />
                <Pressable
                  style={styles.eyeIcon}
                  onPress={() => setShowPassword(!showPassword)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  accessible={true}
                  accessibilityRole="button"
                  accessibilityLabel={
                    showPassword ? "إخفاء كلمة المرور" : "إظهار كلمة المرور"
                  }
                >
                  <MaterialIcons
                    name={showPassword ? "visibility-off" : "visibility"}
                    size={24}
                    color="#999"
                  />
                </Pressable>
              </View>
            </View>

            <Pressable
              style={styles.connectButton}
              onPress={handleConnect}
              android_ripple={{
                color: Colors.primary50,
                overflow: "hidden",
              }}
            >
              <Text style={styles.connectButtonText}>تسجيل الدخول</Text>
            </Pressable>
            <View style={styles.separatorContainer}>
              <View style={styles.separatorLine} />
              <Text style={styles.separatorText}>أو</Text>
              <View style={styles.separatorLine} />
            </View>
            <Pressable
              style={styles.DidButton}
              onPress={handleConnect}
              android_ripple={{
                color: Colors.primary50,
                overflow: "hidden",
              }}
            >
              <View style={styles.DidButtonContent}>
                <Text style={styles.DidButtonText}>
                  تسجيل الدخول بالهوية الرقمية
                </Text>
                <Image
                  source={require("../../assets/Sofa-Chat/Emblem-of-Yemen.png")}
                  style={styles.yemenEmblem}
                />
              </View>
            </Pressable>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default LogIn;

const styles = StyleSheet.create({
  LinearGradientContainer: {
    flex: 1,
    alignItems: "center",
    paddingTop: height * 0.08,
    paddingBottom: height * 0.14,
  },
  container: {
    flex: 1,
    width: "100%",
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 100,
    minHeight: height,
  },
  content: {
    width: width * 0.9,
    alignSelf: "center",
    padding: 16,
    borderRadius: 16,
    marginBottom: 50,
    paddingTop: 20,
  },
  subtitle: {
    fontSize: 18,
    textAlign: "right",
    marginBottom: 8,
    marginTop: 40,
    color: "#2C3E50",
    writingDirection: "rtl",
    fontFamily: "Tajawal_800ExtraBold",
  },
  instruction: {
    fontSize: 15,
    color: "#7F8C8D",
    textAlign: "right",
    marginBottom: 18,
    writingDirection: "rtl",
    lineHeight: 16,
    fontFamily: "Tajawal_400Regular",
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 10,
    color: "#34495E",
    textAlign: "right",
    writingDirection: "rtl",
    fontFamily: "Tajawal_700Bold",
  },
  input: {
    borderWidth: 1,
    borderColor: "#CCCCCC",
    borderRadius: 10,
    padding: 8,
    fontSize: 14,
    height: 50,
    width: "110%",
    marginLeft: -10,
    backgroundColor: "#FFFFFF",
    alignContent: "center",
    textAlign: "right",
    writingDirection: "rtl",
    fontFamily: "Tajawal_400Regular",
  },
  passwordContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#CCCCCC",
    borderRadius: 10,
    backgroundColor: "#FFFFFF",
    width: "110%",
    marginLeft: -10,
  },
  passwordInput: {
    flex: 1,
    padding: 8,
    fontSize: 14,
    height: 50,
    textAlign: "right",
    writingDirection: "rtl",
    fontFamily: "Tajawal_300Light",
  },
  eyeIcon: {
    paddingHorizontal: 12,
  },
  separatorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 8,
  },
  separatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.gray700,
  },
  separatorText: {
    marginHorizontal: 10,
    color: Colors.gray500,
    fontFamily: "Tajawal_500Medium",
    fontSize: 16,
  },
  connectButton: {
    backgroundColor: Colors.primary700,
    borderRadius: 10,
    padding: 16,
    alignItems: "center",
    marginTop: 20,
    height: 60,
    justifyContent: "center",
  },
  connectButtonText: {
    color: Colors.primary50,
    fontSize: 18,
    fontFamily: "Tajawal_500Medium",
  },
  DidButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: Colors.gray500,
    borderRadius: 10,
    padding: 4,
    alignItems: "center",
    marginTop: 5,
    height: 40,
    justifyContent: "center",
  },
  DidButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  yemenEmblem: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  DidButtonText: {
    color: Colors.gray700,
    fontSize: 17,
    marginRight: 8,
    fontFamily: "Tajawal_500Medium",
  },
});

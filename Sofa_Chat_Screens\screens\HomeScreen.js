import { useState } from 'react';
import { View, Text, StyleSheet, Image, FlatList, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../components/Colors';

const dummyUsers = [
  { id: 1, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user1.png') },
  { id: 2, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user2.png') },
  { id: 3, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user3.png') },
  { id: 4, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user4.png') },
  { id: 5, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user5.png') },
  { id: 6, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user6.png') },
  { id: 7, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user7.png') },
  { id: 8, name: 'واحد', avatar: require('../assets/Sofa-Chat/user_avatar/user8.png') },
];

const HomeScreen = () => {
  const [activeTab, setActiveTab] = useState('direct');

  const renderTopProfiles = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.topProfileContainer}>
      {dummyUsers.map((user, index) => (
        <View key={index} style={styles.profileItem}>
          <Image source={user.avatar} style={styles.profileImage} />
          <Text style={styles.profileName}>سيف</Text>
        </View>
      ))}
      <TouchableOpacity style={styles.addProfile}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </ScrollView>
  );

  const renderMessageItem = ({ item }) => (
    <View style={styles.messageItem}>
      <View style={styles.avatarCircle}>
        <Image source={item.avatar} style={styles.avatarImage} />
      </View>
      <View style={styles.messageTextContainer}>
        <Text style={styles.messageName}>{item.name}</Text>
        <Text style={styles.messageText}>سلام عليكم</Text>
      </View>
      <View style={styles.messageMeta}>
        <Text style={styles.messageTime}>12m</Text>
        <View style={styles.badge}>
          <Text style={styles.badgeText}>2</Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Ionicons name="mail-outline" size={24} />
        <Text style={styles.headerText}>الفريق الرئيسي</Text>
      </View>

      {/* Top Profile Stories */}
      {renderTopProfiles()}

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity onPress={() => setActiveTab('direct')}>
          <Text style={[styles.tabText, activeTab === 'direct' && styles.activeTab]}>الرسائل المباشرة</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setActiveTab('channels')}>
          <Text style={[styles.tabText, activeTab === 'channels' && styles.activeTab]}>القنوات</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setActiveTab('archived')}>
          <Text style={[styles.tabText, activeTab === 'archived' && styles.activeTab]}>المؤرشفة</Text>
        </TouchableOpacity>
      </View>

      {/* Message List */}
      <FlatList
        data={dummyUsers}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderMessageItem}
        contentContainerStyle={{ paddingBottom:  }}
      />
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
    backgroundColor: '#fff',
    direction: 'rtl',
  },
  header: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  headerText: {
    fontSize: 18,
    marginRight: 10,
  },
  topProfileContainer: {
    paddingHorizontal: 10,
    marginVertical: 20,
  },
  profileItem: {
    alignItems: 'center',
    marginHorizontal: 8,
    marginBottom: 30,
  },
  profileImage: {
    width: 55,
    height: 55,
    borderRadius: 30,
    marginBottom: 4,
  },
  profileName: {
    fontSize: 12,
  },
  addProfile: {
    width: 55,
    height: 55,
    borderRadius: 30,
    backgroundColor: '#0B735F',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
    marginTop: 5,
  },
  tabs: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    borderColor: '#eee',
    paddingVertical: 10,
  },
  tabText: {
    fontSize: 14,
    color: '#777',
  },
  activeTab: {
    color: '#0B735F',
    borderBottomWidth: 2,
    borderBottomColor: '#0B735F',
    paddingBottom: 5,
  },
  messageItem: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: '#f0f0f0',
  },
  avatarCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  avatarImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  messageTextContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
  messageName: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  messageText: {
    fontSize: 12,
    color: '#777',
    textAlign: 'right',
  },
  messageMeta: {
    alignItems: 'flex-end',
  },
  messageTime: {
    fontSize: 12,
    color: '#aaa',
  },
  badge: {
    backgroundColor: Colors.primary,
    borderRadius: 10,
    paddingHorizontal: 6,
    marginTop: 4,
  },
  badgeText: {
    color: Colors.primary50,
    fontSize: 12,
  },
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    height: 60,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderColor: '#eee',
    flexDirection: 'row-reverse',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
});

{"version": 3, "file": "SplashScreen.types.js", "sourceRoot": "", "sources": ["../src/SplashScreen.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { NativeModule } from 'expo-modules-core/types';\n\nexport type SplashScreenOptions = {\n  /**\n   * The duration of the fade out animation in milliseconds.\n   * @default 400\n   */\n  duration?: number;\n  /**\n   * Whether to hide the splash screen with a fade out animation.\n   * @platform ios\n   * @default false\n   */\n  fade?: boolean;\n};\n\nexport interface SplashScreenNativeModule extends NativeModule {\n  setOptions: (options: SplashScreenOptions) => void;\n  preventAutoHideAsync: () => Promise<boolean>;\n  hide: () => void;\n  hideAsync: () => Promise<void>;\n  // @private\n  _internal_maybeHideAsync: () => Promise<void>;\n  // @private\n  _internal_preventAutoHideAsync: () => Promise<boolean>;\n}\n"]}
import { useState } from "react";
import {
  StyleSheet,
  View,
  FlatList,
  Button,
} from "react-native";
import { StatusBar } from "expo-status-bar";

import GoalInput from "./components/GoalInput";
import GoalItems from "./components/GoalItems";

export default function App() {
  const [enteredGoals, setEnteredGoals] = useState([]);
  const [modalIsVisible, setModalIsVisible] = useState(false);

  function startAddGoalHandler() {
    setModalIsVisible(true);
  }
  
  function endAddGoalHandler() {
    setModalIsVisible(false);
  }

  function addGoalHandler(enteredGoalText) {
    setEnteredGoals((currentGoals) => [
      ...currentGoals,
      { text: enteredGoalText, id: Math.random().toString() },
    ]);
    endAddGoalHandler();
  }
  function deleteGoalHandler(id) {
    setEnteredGoals(currentGoals => {
      return currentGoals.filter((goal) => goal.id !== id);
    });
  }
  return (
    <>
    <StatusBar style="dark" />
    <View style={styles.appContainer}>
      <Button color="orange" title="Add New Goal" onPress={startAddGoalHandler} />
      <GoalInput 
      visible={modalIsVisible} 
      onAddGoal={addGoalHandler} 
      onCancel={endAddGoalHandler}
      />
      <View style={styles.goalList}>
        <FlatList
          data={enteredGoals}
          renderItem={(itemData) => {
            return <GoalItems 
            id={itemData.item.id} 
            text={itemData.item.text} 
            onDeleteGoal={deleteGoalHandler} 
            />;
          }}
          keyExtractor={(item, index) => {
            return item.id;
          }}
        />
      </View>
    </View>
    </>
  );
}

const styles = StyleSheet.create({
  appContainer: {
    backgroundColor: "lightgreen",
    flex: 1,
    padding: 20,
    marginTop: 30,
  },
  goalList: {
    flex: 5,
  },
});

import { View, StyleSheet } from 'react-native';

export const TopRightRectangles = () => {
  return (
    <>
      <View style={[styles.rectangle, styles.topRight1]} />
      <View style={[styles.rectangle, styles.topRight2]} />
      <View style={[styles.rectangle, styles.topRight3]} />
      <View style={[styles.rectangle, styles.topRight4]} />
      <View style={[styles.rectangle, styles.topRight5]} />
      <View style={[styles.rectangle, styles.topRight6]} />
      <View style={[styles.rectangle, styles.topRight7]} />
      <View style={[styles.rectangle, styles.topRight8]} />
    </>
  );
};

const styles = StyleSheet.create({
  rectangle: {
    position: "absolute",
    width: 60,
    height: 60,
    marginBottom: 10,
    backgroundColor: "transparent",
    borderWidth: 1.5,
    borderColor: "rgba(36, 203, 170, 0.58)",
    borderRadius: 10,
  },
  // Top-Right Styles
  topRight1: {
    top: 50, right: 50,
    marginRight: -90, marginTop: -55,
    opacity: 0.2,
  },
  topRight2: {
    top: 60, right: 60,
    marginTop: -65, marginRight: -40,
    borderRightWidth: 0, borderBottomWidth: 0,
    borderBottomLeftRadius: 10, opacity: 0.3,
  },
  topRight3: {
    top: 75, right: 75,
    marginRight: -60.5, marginTop: -22,
    borderRightWidth: 1.5, borderBottomWidth: 1.5,
    borderTopLeftRadius: 0, borderBottomLeftRadius: 10,
    borderLeftWidth: 0, opacity: 0.5,
  },
  topRight4: {
    top: 90, right: 90,
    width: 65, height: 53,
    marginRight: -20, marginTop: -33,
    borderRightWidth: 1.5, borderBottomWidth: 1.5,
    borderBottomRightRadius: 0, borderBottomLeftRadius: 10,
    borderBottomWidth: 0, opacity: 0.6,
  },
  topRight5: {
    top: 105, right: 105,
    width: 55, height: 20,
    marginRight: -31, marginTop: 3,
    borderBottomRightRadius: 0, borderTopLeftRadius: 0,
    borderBottomWidth: 0, borderLeftWidth: 0, opacity: 0.8,
  },
  topRight6: {
    top: 120, right: 120,
    width: 50, height: 40,
    marginRight: -100, marginTop: 35.7,
    borderBottomRightRadius: 0, borderBottomWidth: 0,
    borderTopWidth: 0, borderLeftWidth: 0, opacity: 0.8,
  },
  topRight7: {
    top: 120, right: 120,
    width: 55, height: 30,
    marginRight: -99.5, marginTop: 8.1,
    borderTopLeftRadius: 0, borderTopWidth: 0,
    borderRightWidth: 0, opacity: 0.6,
  },
  topRight8: {
    top: 135, right: 135,
    width: 55, height: 10,
    marginRight: -168.2, marginTop: 60.5,
    borderTopLeftRadius: 0, borderTopWidth: 0,
    borderRightWidth: 0, opacity: 0.3,
  }
});
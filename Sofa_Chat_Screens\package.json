{"name": "sofa_chat_screens", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/tajawal": "^0.4.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "expo": "~53.0.9", "expo-linear-gradient": "~14.1.4", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-linear-gradient": "^2.8.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.5.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}